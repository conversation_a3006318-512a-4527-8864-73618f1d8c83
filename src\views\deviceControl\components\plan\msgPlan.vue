<template>
  <div class="msgPlan" v-loading="loading">
    <div class="msgPlan-list">
      <div class="msgPlan-item" v-for="(item, index) in formList" :key="index">
        <div style="min-width: 500px">
          <div class="msgPlan-header">
            <div class="flex" style="gap: 0">
              <span>消息定时计划</span>
              <div class="flex">
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  content="在下述生效规则下，生效设备按设定规则自动发送定时消息"
                  placement="top-start"
                >
                  <el-icon :size="16" style="margin-left: 5px"
                    ><QuestionFilled
                  /></el-icon>
                </el-tooltip>
                <el-tooltip content="修改" placement="top">
                  <el-button
                    v-show="!item.isEdit"
                    type="primary"
                    style="font-size: 20px; margin-left: 10px"
                    icon="Edit"
                    :disabled="item.disabled"
                    link
                    @click="handleEdit(index)"
                  ></el-button>
                </el-tooltip>
              </div>
            </div>
            <el-tooltip content="删除" placement="top">
              <el-button
                v-show="!item.isEdit && !item.id && formList.length > 1"
                type="danger"
                style="font-size: 20px"
                icon="Delete"
                link
                @click="handleDel(index)"
              ></el-button>
            </el-tooltip>
          </div>
          <div
            v-show="item.isEdit || !!item.id"
            class="msgPlan-content"
            :class="{ 'msgPlan-content_unedit': !item.isEdit }"
          >
            <div>
              <el-form
                :ref="(el) => setFormRef(el, index)"
                :model="item"
                :label-width="item.isEdit ? '92px' : '82px'"
              >
                <el-form-item
                  :label="item.isEdit ? '启动计划：' : '启用状态：'"
                  prop="status"
                  label-width="82px"
                >
                  <el-switch
                    v-if="item.isEdit"
                    v-model="item.status"
                    :active-value="0"
                    :inactive-value="1"
                    @change="(val) => handleStatusChange(val, index)"
                  />
                  <span v-else>{{
                    item.status === 0 ? "已启用" : "已禁用"
                  }}</span>
                </el-form-item>
                <el-form-item
                  label="消息类型："
                  prop="isTop"
                  :rules="{
                    required: item.isEdit,
                    message: '请选择消息类型',
                    trigger: 'change',
                  }"
                >
                  <el-radio-group
                    v-model="item.isTop"
                    v-if="item.isEdit"
                    :disabled="!!item.id && item.status === 1"
                  >
                    <el-radio :value="false">日常通知（非应用置顶）</el-radio>
                    <el-radio :value="true">霸屏显示（应用置顶）</el-radio>
                  </el-radio-group>
                  <span v-else>{{
                    !item.isTop
                      ? "日常通知（非应用置顶）"
                      : "霸屏显示（应用置顶）"
                  }}</span>
                </el-form-item>

                <el-form-item
                  label="生效日期："
                  prop="effectiveDate"
                  :rules="{
                    required: item.isEdit,
                    validator: (rule, value, callback) =>
                      validEffectiveDate(rule, value, callback, index),
                    trigger: 'change',
                  }"
                >
                  <el-date-picker
                    v-if="item.isEdit"
                    v-model="item.effectiveDate"
                    type="date"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    placeholder="请选择日期"
                    :disabled-date="disabledDate"
                    @change="handleDateChange(index)"
                    :disabled="!!item.id && item.status === 1"
                  />
                  <span v-else>{{ item.effectiveDate || "-" }}</span>
                </el-form-item>

                <el-form-item
                  label="生效时间："
                  prop="effectiveTime"
                  :rules="{
                    required: item.isEdit,
                    validator: (rule, value, callback) =>
                      validEffectiveTime(rule, value, callback, index),
                    trigger: 'change',
                  }"
                >
                  <el-time-picker
                    v-if="item.isEdit"
                    v-model="item.effectiveTime"
                    placeholder="请选择时间"
                    format="HH:mm:ss"
                    value-format="HH:mm:ss"
                    :disabled="!!item.id && item.status === 1"
                  />
                  <span v-else>{{ item.effectiveTime || "-" }}</span>
                </el-form-item>

                <el-form-item
                  label="持续时间："
                  prop="lastTimeNum"
                  :rules="{
                    required: item.isEdit,
                    validator: (rule, value, callback) =>
                      validDuration(rule, value, callback, index),
                    trigger: 'blur',
                  }"
                >
                  <div v-if="item.isEdit">
                    <el-input-number
                      v-model="item.lastTimeNum"
                      :min="1"
                      :max="60"
                      :precision="0"
                      controls-position="right"
                      style="width: 200px"
                      :disabled="!!item.id && item.status === 1"
                    />
                    <el-select
                      v-model="item.lastTimeUnit"
                      style="margin-left: 10px; width: 80px"
                      :disabled="!!item.id && item.status === 1"
                      @change="() => handleTimeUnitChange(index)"
                    >
                      <el-option label="秒" :value="1" />
                      <el-option label="分钟" :value="2" />
                      <el-option label="小时" :value="3" />
                      <el-option label="天" :value="4" />
                    </el-select>
                  </div>
                  <span v-else
                    >{{ item.lastTimeNum || 1
                    }}{{ unitObj[item.lastTimeUnit] || "秒" }}</span
                  >
                </el-form-item>

                <el-form-item
                  label="轮播时间："
                  prop="loopTime"
                  :rules="{
                    required: item.isEdit,
                    validator: (rule, value, callback) =>
                      validLoopTime(rule, value, callback, index),
                    trigger: 'blur',
                  }"
                >
                  <div v-if="item.isEdit">
                    <el-input-number
                      v-model="item.loopTime"
                      :min="1"
                      :max="60"
                      :precision="0"
                      controls-position="right"
                      style="width: 200px"
                      :disabled="!!item.id && item.status === 1"
                    />
                    <el-select
                      v-model="item.carouselUnit"
                      style="margin-left: 10px; width: 80px"
                      :disabled="!!item.id && item.status === 1"
                      @change="() => handleTimeUnitChange(index)"
                    >
                      <el-option label="秒" :value="1" />
                      <el-option label="分钟" :value="2" />
                      <el-option label="小时" :value="3" />
                      <el-option label="天" :value="4" />
                    </el-select>
                  </div>
                  <span v-else
                    >{{ item.loopTime || 1
                    }}{{ unitObj[item.carouselUnit] || "秒" }}</span
                  >
                </el-form-item>

                <el-form-item
                  label="消息内容："
                  prop="message"
                  :rules="{
                    required: item.isEdit,
                    validator: (rule, value, callback) =>
                      validMessageContent(rule, value, callback, index),
                    trigger: 'blur',
                  }"
                >
                  <el-input
                    v-if="item.isEdit"
                    v-model="item.message"
                    type="textarea"
                    :rows="4"
                    maxlength="50"
                    placeholder="请输入消息内容"
                    @keydown.enter.native="
                      (event) => SendEventOne(event, index)
                    "
                    @paste.native="(event) => handlePaste(event, index)"
                    show-word-limit
                    style="width: 500px"
                    :disabled="!!item.id && item.status === 1"
                  />
                  <span v-else>{{ item.message || "-" }}</span>
                </el-form-item>

                <el-form-item
                  label="生效设备："
                  prop="selectedDevices"
                  :rules="{
                    required: item.isEdit,
                    validator: (rule, value, callback) =>
                      validSelectedDevices(rule, value, callback, index),
                    trigger: 'change',
                  }"
                >
                  <deviceSel
                    v-model="item.selectedDevices"
                    :isEdit="item.isEdit"
                    :device-codes="item.deviceCodes"
                    :disabled="!!item.id && item.status === 1"
                    @valid="handleValid(index)"
                    @change="(val) => handleChangeSelectedDevices(val, index)"
                  />
                </el-form-item>
              </el-form>
              <div class="msgPlan-footer" v-if="item.isEdit">
                <el-button type="primary" @click="submitForm(index)" v-throttle
                  >保存</el-button
                >
                <el-button @click="handleCancel(index)">取消</el-button>
              </div>
            </div>

            <div v-show="!item.isEdit && (!!item.id || formList.length > 1)">
              <el-tooltip content="删除" placement="top">
                <el-button
                  type="danger"
                  style="font-size: 20px"
                  icon="Delete"
                  link
                  @click="handleDel(index)"
                ></el-button>
              </el-tooltip>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="msgPlan-add"
      v-if="formList.length > 0 && showAdd"
      @click="handleAdd"
    >
      <el-icon><Plus /></el-icon> 新增计划，最多五个计划
    </div>
  </div>
</template>

<script setup>
import deviceSel from "../deviceSel.vue";
import { ref, reactive, toRefs, getCurrentInstance, nextTick } from "vue";
import {
  sendMessageMqttOrHttp,
  listTimePlan,
  deleteTimePlan,
} from "@/api/deviceControl";
import { timeFormat } from "@/utils";

const { proxy } = getCurrentInstance();

const formRefs = ref({});
// 设置表单引用的函数
const setFormRef = (el, index) => {
  if (el) {
    formRefs.value[index] = el;
  } else {
    delete formRefs.value[index];
  }
};
const showAdd = computed(() => {
  let flag = formList.value.every((_) => !!_.id && !_.isEdit);
  return formList.value.length < 5 && flag;
});
const state = reactive({
  loading: false,
  initFormList: [],
  formList: [],
  unitObj: {
    1: "秒",
    2: "分钟",
    3: "小时",
    4: "天",
  },
  queryParams: {
    current: 1,
    pageSize: 10,
  },
});
const { initFormList, formList, unitObj, loading, queryParams } = toRefs(state);

// watch(
//   () => state.formList,
//   (val) => {
//     let flag = val.every((_) => !_.isEdit);
//     console.log('flag', flag)
//     if (flag) {
//       state.formList.forEach((item) => {
//         item.disabled = false;
//       });
//     } else {
//       state.formList.forEach((item) => {
//         if (!item.isEdit) {
//           item.disabled = true;
//         }
//       });
//     }
//   },
//   {
//     deep: true
//   }
// );

onMounted(() => {
  getList();
});

const handleValid = (index) => {
  formRefs.value[index].validateField(`selectedDevices`);
};

const handleChangeSelectedDevices = (val, index) => {
  state.formList[index].selectedDevices = val?.join(",");
};

function SendEventOne(event) {
  if (event.key === "Enter" || event.keyCode === 13) {
    event.preventDefault(); // 打印这个为undefined
    return false;
  }
}

function handlePaste(event, index) {
  event.preventDefault();
  // 获取粘贴板内容并移除换行符
  const pasteData = (event.clipboardData || window.clipboardData).getData(
    "text"
  );
  const cleanText = pasteData.replace(/[\r\n]/g, "");

  // 插入处理后的文本
  insertTextAtCursor(cleanText, index);
}

// 在光标位置插入文本
const insertTextAtCursor = (text, index) => {
  const inputElement = document.activeElement;
  const startPos = inputElement.selectionStart;
  const endPos = inputElement.selectionEnd;

  // 计算新值（考虑当前选择区域）
  const newValue =
    state.formList[index].message.substring(0, startPos) +
    text +
    state.formList[index].message.substring(endPos);

  // 确保不超过50字符
  if (newValue.length <= 50) {
    state.formList[index].message = newValue;

    // 更新光标位置（放在 nextTick 中确保 DOM 更新）
    setTimeout(() => {
      inputElement.setSelectionRange(
        startPos + text.length,
        startPos + text.length
      );
    }, 0);
  } else {
    // 超过长度时截取有效部分
    const allowedLength =
      50 - state.formList[index].message.length + (endPos - startPos);
    const partialText = text.substring(0, allowedLength);

    state.formList[index].message =
      state.formList[index].message.substring(0, startPos) +
      partialText +
      state.formList[index].message.substring(endPos);

    setTimeout(() => {
      inputElement.setSelectionRange(
        startPos + partialText.length,
        startPos + partialText.length
      );
    }, 0);
  }
};

function getList() {
  loading.value = true;
  listTimePlan(queryParams.value)
    .then((res) => {
      if (res.code === 200) {
        console.log(res.data, "消息定时计划列表");
        if (res.data?.messageTimePlanVOList?.length > 0) {
          formList.value = res.data?.messageTimePlanVOList?.map((item) => {
            let carouselSeconds = item.second;
            switch (item.carouselUnit) {
              case 2: // 分钟
                carouselSeconds = item.second / 60;
                break;
              case 3: // 小时
                carouselSeconds = item.second / 60 / 60;
                break;
              case 4: // 天
                carouselSeconds = item.second / 24 / 60 / 60;
                break;
              default: // 秒
                carouselSeconds = item.second;
            }
            let arr = item.noticeTime?.split(" ") || [];
            let obj = {
              ...item,
              version: item.version || 1,
              isEdit: false,
              disabled: false,
              effectiveDate: arr[0] || "",
              effectiveTime: arr[1] || "",
              message: item.content,
              loopTime: carouselSeconds,
              selectedDevices: item.deviceCodes.join(","),
            };
            return obj;
          });
        } else {
          formList.value = [
            {
              disabled: false,
              isEdit: false,
              id: "",
              isTop: false,
              status: 1,
              effectiveDate: "",
              effectiveTime: "",
              lastTimeNum: 1,
              lastTimeUnit: 1,
              loopTime: 1,
              carouselUnit: 1,
              message: "",
              selectedDevices: "",
              deviceCodes: [],
              version: 1,
            },
          ];
        }

        initFormList.value = JSON.parse(JSON.stringify(formList.value));
      }
    })
    .finally(() => {
      loading.value = false;
    });
}

// 校验生效日期
const validEffectiveDate = (rule, value, callback, index) => {
  // 如果状态为禁用，跳过验证
  if (state.formList[index].status === 1 && !!state.formList[index].id) {
    callback();
    return;
  }

  if (value) {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const selectedDate = new Date(value);
    selectedDate.setHours(0, 0, 0, 0);
    if (selectedDate.getTime() < today.getTime()) {
      callback(new Error("生效日期不能早于今天"));
      return;
    }
  }

  if (!value) {
    callback(new Error("请选择生效日期"));
    return;
  }

  callback();
};
// 校验生效时间
const validEffectiveTime = (rule, value, callback, index) => {
  // 如果状态为禁用，跳过验证
  if (state.formList[index].status === 1 && !!state.formList[index].id) {
    callback();
    return;
  }

  let now = new Date();

  if (!value) {
    callback(new Error("请选择生效时间"));
  } else if (
    state.formList[index].effectiveDate &&
    now.getTime() >
      new Date(`${state.formList[index].effectiveDate} ${value}`).getTime()
  ) {
    callback(new Error("生效时间不能小于当前时间"));
  } else {
    callback();
  }
};
// 校验持续时间
const validDuration = (rule, value, callback, index) => {
  // 如果状态为禁用，跳过验证
  if (state.formList[index].status === 1 && !!state.formList[index].id) {
    callback();
    return;
  }
  if (!value) {
    callback(new Error("请输入持续时间"));
  } else {
    callback();
    // 持续时间改变后，重新验证轮播时间
    nextTick(() => {
      if (formRefs.value[index] && state.formList[index].loopTime) {
        formRefs.value[index].validateField('loopTime');
      }
    });
  }
};
// 校验轮播时间
const validLoopTime = (rule, value, callback, index) => {
  // 如果状态为禁用，跳过验证
  if (state.formList[index].status === 1 && !!state.formList[index].id) {
    callback();
    return;
  }

  if (!value) {
    callback(new Error("请输入轮播时间"));
    return;
  }

  // 检查持续时间是否已设置
  if (!state.formList[index].lastTimeNum) {
    callback();
    return;
  }

  // 将轮播时间和持续时间都转换为秒为单位进行比较
  let loopTimeInSeconds;
  let durationInSeconds;

  // 计算轮播时间的秒数
  switch(state.formList[index].carouselUnit){
    case 2: // 分钟
      loopTimeInSeconds = value * 60;
      break;
    case 3: // 小时
      loopTimeInSeconds = value * 60 * 60;
      break;
    case 4: // 天
      loopTimeInSeconds = value * 24 * 60 * 60;
      break;
    default: // 秒
      loopTimeInSeconds = value;
  }

  // 计算持续时间的秒数
  switch(state.formList[index].lastTimeUnit){
    case 2: // 分钟
      durationInSeconds = state.formList[index].lastTimeNum * 60;
      break;
    case 3: // 小时
      durationInSeconds = state.formList[index].lastTimeNum * 60 * 60;
      break;
    case 4: // 天
      durationInSeconds = state.formList[index].lastTimeNum * 24 * 60 * 60;
      break;
    default: // 秒
      durationInSeconds = state.formList[index].lastTimeNum;
  }

  // 比较轮播时间和持续时间
  if (loopTimeInSeconds > durationInSeconds) {
    callback(new Error("轮播时间不能大于持续时间"));
  } else {
    callback();
  }
};

// 校验消息内容
const validMessageContent = (rule, value, callback, index) => {
  // 如果状态为禁用，跳过验证
  if (state.formList[index].status === 1 && !!state.formList[index].id) {
    callback();
    return;
  }
  if (!value) {
    callback(new Error("请输入消息内容"));
  } else {
    callback();
  }
};
// 校验生效设备
const validSelectedDevices = (rule, value, callback, index) => {
  // 如果状态为禁用，跳过验证
  if (state.formList[index].status === 1 && !!state.formList[index].id) {
    callback();
    return;
  }
  if (!value) {
    callback(new Error("请选择生效设备"));
  } else {
    callback();
  }
};

const handleStatusChange = (val, index) => {
  let obj = state.formList[index];
  obj.isEdit = true;
  if (val === 1) formRefs.value[index]?.clearValidate();
};

const handleAdd = () => {
  state.formList.push({
    disabled: false,
    isEdit: false,
    id: "",
    isTop: false,
    status: 1,
    effectiveDate: "",
    effectiveTime: "",
    lastTimeNum: 1,
    lastTimeUnit: 1,
    loopTime: 1,
    carouselUnit: 1,
    message: "",
    selectedDevices: "",
    deviceCodes: [],
    version: 1,
  });
};

const handleEdit = (index) => {
  state.formList[index].isEdit = true;
  if (!state.formList[index].id) state.formList[index].status = 0;
};

const handleDel = (index) => {
  if (state.formList[index].id) {
    proxy.$modal
      .confirm("删除后已设置的设备重启后失效，是否确认删除？")
      .then((res) => {
        loading.value = true;
        deleteTimePlan({
          id: state.formList[index].id,
        })
          .then((res) => {
            if (res.code === 200) {
              state.formList.splice(index, 1);
              proxy.$modal.msgSuccess("删除成功");
              getList(); // 重新获取列表数据
            }
          })
          .catch(() => {
            // proxy.$modal.msgError('删除失败')
          })
          .finally(() => {
            loading.value = false;
          });
      });
  } else {
    if (index == 0 && state.formList.length == 1) {
      resetForm(index);
    } else {
      state.formList.splice(index, 1);
    }
  }
};

const handleCancel = (index) => {
  if (formRefs.value[index]) {
    formRefs.value[index].resetFields();
  }
  state.formList[index].isEdit = false;
  if (!state.formList[index].id) {
    resetForm(index);
  } else {
    state.formList[index] = JSON.parse(
      JSON.stringify(initFormList.value[index])
    );
  }
};

const handleDateChange = (index) => {
  if (
    state.formList[index].effectiveDate &&
    state.formList[index].effectiveTime
  ) {
    formRefs.value[index].validateField("effectiveTime");
  }
};

// 处理时间单位变化
const handleTimeUnitChange = (index) => {
  nextTick(() => {
    if (formRefs.value[index] && state.formList[index].loopTime) {
      formRefs.value[index].validateField('loopTime');
    }
  });
};

const submitForm = (index) => {
  console.log(formRefs.value[index]);
  if (state.formList[index].status === 1 && !state.formList[index].id) {
    proxy.$modal.alert("请先开启启动计划再保存");
    return;
  }
  if (!formRefs.value[index]) return;
  nextTick(() => {
    formRefs.value[index]?.validate((valid) => {
      if (valid) {
        const {
          isTop,
          message,
          loopTime,
          carouselUnit,
          effectiveDate,
          effectiveTime,
          selectedDevices,
          lastTimeNum,
          lastTimeUnit,
          version,
        } =
          state[
            state.formList[index].status === 1 && !!state.formList[index].id
              ? "initFormList"
              : "formList"
          ][index];
        // 计算轮播时间的秒数
        let carouselSeconds = loopTime;
        switch (carouselUnit) {
          case 2: // 分钟
            carouselSeconds = loopTime * 60;
            break;
          case 3: // 小时
            carouselSeconds = loopTime * 60 * 60;
            break;
          case 4: // 天
            carouselSeconds = loopTime * 24 * 60 * 60;
            break;
          default: // 秒
            carouselSeconds = loopTime;
        }

        // 处理生效时间
        let effectiveDateTime = null;
        if (effectiveDate && effectiveTime) {
          const now = new Date();
          const selectedDateTime = new Date(
            `${effectiveDate} ${effectiveTime}`
          );

          if (selectedDateTime.getTime() <= now.getTime()) {
            const adjustedTime = new Date(now.getTime() + 5000);
            effectiveDateTime = timeFormat(adjustedTime, "yyyy-mm-dd hh:MM:ss");
          } else {
            effectiveDateTime = timeFormat(
              selectedDateTime,
              "yyyy-mm-dd hh:MM:ss"
            );
          }
        }

        let submitData = {
          status: state.formList[index].status,
          deviceCodeList: selectedDevices
            ? selectedDevices.split(",").filter(Boolean)
            : [""],
          message: message || "",
          isTop,
          version,
          noticeTime: effectiveDateTime,
          lastTimeNum: lastTimeNum || 1,
          lastTimeUnit: Number(lastTimeUnit || 1),
          second: carouselSeconds || 1,
          carouselUnit: Number(carouselUnit || 1),
        };

        if (state.formList[index].id) {
          submitData.id = state.formList[index].id;
        }

        console.log("提交数据:", submitData, JSON.stringify(submitData));
        // 调用接口
        loading.value = true;
        sendMessageMqttOrHttp(submitData)
          .then((res) => {
            if (res.code === 200) {
              proxy.$modal.msgSuccess("操作成功");
              getList();
            }
          })
          .catch((e) => {
            console.log(e, "保存失败");
            getList();
          })
          .finally(() => (loading.value = false));
      }
    });
  });
};

const resetForm = (index) => {
  state.formList[index] = {
    disabled: false,
    isEdit: false,
    id: "",
    isTop: false,
    status: 1,
    effectiveDate: "",
    effectiveTime: "",
    lastTimeNum: 1,
    lastTimeUnit: 1,
    loopTime: 1,
    carouselUnit: 1,
    message: "",
    selectedDevices: "",
    deviceCodes: [],
    version: 1,
  };
};
// 添加日期限制方法
const disabledDate = (time) => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return time.getTime() < today.getTime();
};
</script>

<style scoped lang="scss">
.flex {
  display: flex;
  align-items: center;
  gap: 0 5px;
}
.msgPlan {
  font-size: 14px;

  &-list {
    display: flex;
    flex-direction: column;
    gap: 15px 0;
  }

  &-add {
    border: 1px solid #e5e5e5;
    border-top: none;
    padding: 10px;
    color: rgb(152, 152, 152);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0 10px;
    &:hover {
      color: #7e96ae;
      background-color: #f8faff;
    }
  }

  &-item {
    border: 1px solid #e5e5e5;
  }

  &-content {
    padding: 10px 20px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    &_unedit {
      padding-bottom: 10px;
      :deep(.el-form-item--default) {
        margin-bottom: 0px;
      }
    }
  }

  &-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 16px;
    padding: 10px 20px;
    border-bottom: 1px solid #e5e5e5;
  }

  &-footer {
    min-width: 500px;
    text-align: center;
  }
}
</style>